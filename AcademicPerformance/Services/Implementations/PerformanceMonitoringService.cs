using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AcademicPerformance.Services.Implementations;

/// <summary>
/// Performance monitoring service implementation
/// In-memory implementation for development, should be replaced with persistent storage for production
/// </summary>
public class PerformanceMonitoringService : IPerformanceMonitoringService
{
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly ConcurrentDictionary<string, List<QueryExecutionRecord>> _queryRecords = new();
    private readonly ConcurrentDictionary<string, List<CacheOperationRecord>> _cacheRecords = new();
    private readonly ConcurrentDictionary<string, List<ApiEndpointRecord>> _apiRecords = new();

    public PerformanceMonitoringService(ILogger<PerformanceMonitoringService> logger)
    {
        _logger = logger;
    }

    public IDisposable StartMeasurement(string operationName, Dictionary<string, object>? properties = null)
    {
        return new PerformanceMeasurement(operationName, properties, this);
    }

    public Task RecordQueryExecutionAsync(string queryName, TimeSpan duration, bool isSuccessful, string? errorMessage = null)
    {
        try
        {
            var record = new QueryExecutionRecord
            {
                QueryName = queryName,
                Duration = duration,
                IsSuccessful = isSuccessful,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.UtcNow
            };

            _queryRecords.AddOrUpdate(queryName,
                new List<QueryExecutionRecord> { record },
                (key, existing) =>
                {
                    existing.Add(record);
                    // Keep only last 1000 records per query
                    if (existing.Count > 1000)
                    {
                        existing.RemoveRange(0, existing.Count - 1000);
                    }
                    return existing;
                });

            // Log slow queries
            if (duration.TotalMilliseconds > 1000)
            {
                _logger.LogWarning("Slow query detected: {QueryName} took {Duration}ms",
                    queryName, duration.TotalMilliseconds);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording query execution for {QueryName}", queryName);
        }

        return Task.CompletedTask;
    }

    public Task RecordCacheOperationAsync(string cacheKey, bool isHit, TimeSpan duration)
    {
        try
        {
            var record = new CacheOperationRecord
            {
                CacheKey = cacheKey,
                IsHit = isHit,
                Duration = duration,
                Timestamp = DateTime.UtcNow
            };

            _cacheRecords.AddOrUpdate(cacheKey,
                new List<CacheOperationRecord> { record },
                (key, existing) =>
                {
                    existing.Add(record);
                    // Keep only last 500 records per key
                    if (existing.Count > 500)
                    {
                        existing.RemoveRange(0, existing.Count - 500);
                    }
                    return existing;
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording cache operation for {CacheKey}", cacheKey);
        }

        return Task.CompletedTask;
    }

    public Task RecordApiEndpointAsync(string endpoint, string method, TimeSpan duration, int statusCode, long? responseSize = null)
    {
        try
        {
            var record = new ApiEndpointRecord
            {
                Endpoint = endpoint,
                Method = method,
                Duration = duration,
                StatusCode = statusCode,
                ResponseSize = responseSize,
                Timestamp = DateTime.UtcNow
            };

            var key = $"{method} {endpoint}";
            _apiRecords.AddOrUpdate(key,
                new List<ApiEndpointRecord> { record },
                (k, existing) =>
                {
                    existing.Add(record);
                    // Keep only last 1000 records per endpoint
                    if (existing.Count > 1000)
                    {
                        existing.RemoveRange(0, existing.Count - 1000);
                    }
                    return existing;
                });

            // Log slow API calls
            if (duration.TotalMilliseconds > 2000)
            {
                _logger.LogWarning("Slow API call detected: {Method} {Endpoint} took {Duration}ms",
                    method, endpoint, duration.TotalMilliseconds);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording API endpoint performance for {Method} {Endpoint}", method, endpoint);
        }

        return Task.CompletedTask;
    }

    public Task<PerformanceMetrics> GetMetricsAsync(DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var fromDate = from ?? DateTime.UtcNow.AddHours(-24);
            var toDate = to ?? DateTime.UtcNow;

            var allApiRecords = _apiRecords.Values
                .SelectMany(records => records)
                .Where(r => r.Timestamp >= fromDate && r.Timestamp <= toDate)
                .ToList();

            if (!allApiRecords.Any())
            {
                return Task.FromResult(new PerformanceMetrics
                {
                    From = fromDate,
                    To = toDate
                });
            }

            var durations = allApiRecords.Select(r => r.Duration.TotalMilliseconds).OrderBy(d => d).ToList();
            var statusCodes = allApiRecords.GroupBy(r => r.StatusCode)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var endpointPerformance = allApiRecords
                .GroupBy(r => $"{r.Method} {r.Endpoint}")
                .ToDictionary(g => g.Key, g => g.Average(r => r.Duration.TotalMilliseconds));

            var metrics = new PerformanceMetrics
            {
                From = fromDate,
                To = toDate,
                TotalRequests = allApiRecords.Count,
                AverageResponseTime = durations.Average(),
                MedianResponseTime = durations[durations.Count / 2],
                P95ResponseTime = durations[(int)(durations.Count * 0.95)],
                P99ResponseTime = durations[(int)(durations.Count * 0.99)],
                ErrorCount = allApiRecords.Count(r => r.StatusCode >= 400),
                StatusCodeDistribution = statusCodes,
                EndpointPerformance = endpointPerformance,
                SlowestEndpoints = endpointPerformance
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .Select(kvp => kvp.Key)
                    .ToList()
            };

            return Task.FromResult(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics");
            return Task.FromResult(new PerformanceMetrics());
        }
    }

    public Task<List<SlowQueryReport>> GetSlowQueriesAsync(TimeSpan threshold, int limit = 100)
    {
        try
        {
            var reports = _queryRecords
                .Where(kvp => kvp.Value.Any(r => r.Duration > threshold))
                .Select(kvp => new SlowQueryReport
                {
                    QueryName = kvp.Key,
                    AverageDuration = TimeSpan.FromMilliseconds(kvp.Value.Average(r => r.Duration.TotalMilliseconds)),
                    MaxDuration = kvp.Value.Max(r => r.Duration),
                    ExecutionCount = kvp.Value.Count,
                    ErrorCount = kvp.Value.Count(r => !r.IsSuccessful),
                    LastExecuted = kvp.Value.Max(r => r.Timestamp),
                    LastError = kvp.Value.Where(r => !r.IsSuccessful).LastOrDefault()?.ErrorMessage
                })
                .OrderByDescending(r => r.AverageDuration)
                .Take(limit)
                .ToList();

            return Task.FromResult(reports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting slow queries report");
            return Task.FromResult(new List<SlowQueryReport>());
        }
    }

    public Task<CachePerformanceReport> GetCachePerformanceAsync()
    {
        try
        {
            var allCacheRecords = _cacheRecords.Values.SelectMany(records => records).ToList();

            if (!allCacheRecords.Any())
            {
                return Task.FromResult(new CachePerformanceReport());
            }

            var hitRecords = allCacheRecords.Where(r => r.IsHit).ToList();
            var missRecords = allCacheRecords.Where(r => !r.IsHit).ToList();

            var keyPatternStats = _cacheRecords
                .GroupBy(kvp => GetKeyPattern(kvp.Key))
                .ToDictionary(g => g.Key, g => (long)g.Sum(kvp => kvp.Value.Count));

            var report = new CachePerformanceReport
            {
                TotalRequests = allCacheRecords.Count,
                HitCount = hitRecords.Count,
                MissCount = missRecords.Count,
                AverageHitTime = hitRecords.Any() ? hitRecords.Average(r => r.Duration.TotalMilliseconds) : 0,
                AverageMissTime = missRecords.Any() ? missRecords.Average(r => r.Duration.TotalMilliseconds) : 0,
                KeyPatternStats = keyPatternStats,
                MostAccessedKeys = _cacheRecords
                    .OrderByDescending(kvp => kvp.Value.Count)
                    .Take(10)
                    .Select(kvp => kvp.Key)
                    .ToList(),
                SlowestKeys = _cacheRecords
                    .OrderByDescending(kvp => kvp.Value.Average(r => r.Duration.TotalMilliseconds))
                    .Take(10)
                    .Select(kvp => kvp.Key)
                    .ToList()
            };

            return Task.FromResult(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache performance report");
            return Task.FromResult(new CachePerformanceReport());
        }
    }

    public Task CleanupOldDataAsync(DateTime olderThan)
    {
        try
        {
            // Clean query records
            foreach (var kvp in _queryRecords.ToList())
            {
                var filteredRecords = kvp.Value.Where(r => r.Timestamp > olderThan).ToList();
                if (filteredRecords.Any())
                {
                    _queryRecords[kvp.Key] = filteredRecords;
                }
                else
                {
                    _queryRecords.TryRemove(kvp.Key, out _);
                }
            }

            // Clean cache records
            foreach (var kvp in _cacheRecords.ToList())
            {
                var filteredRecords = kvp.Value.Where(r => r.Timestamp > olderThan).ToList();
                if (filteredRecords.Any())
                {
                    _cacheRecords[kvp.Key] = filteredRecords;
                }
                else
                {
                    _cacheRecords.TryRemove(kvp.Key, out _);
                }
            }

            // Clean API records
            foreach (var kvp in _apiRecords.ToList())
            {
                var filteredRecords = kvp.Value.Where(r => r.Timestamp > olderThan).ToList();
                if (filteredRecords.Any())
                {
                    _apiRecords[kvp.Key] = filteredRecords;
                }
                else
                {
                    _apiRecords.TryRemove(kvp.Key, out _);
                }
            }

            _logger.LogInformation("Cleaned up performance data older than {OlderThan}", olderThan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old performance data");
        }

        return Task.CompletedTask;
    }

    public Task ReportBatchProgressAsync(string operationName, int currentBatch, int totalBatches)
    {
        try
        {
            var progressPercentage = (double)currentBatch / totalBatches * 100;

            _logger.LogDebug("Bulk operation progress: {OperationName} - {CurrentBatch}/{TotalBatches} ({Progress:F1}%)",
                operationName, currentBatch, totalBatches, progressPercentage);

            // Record progress as a special query execution
            var progressRecord = new QueryExecutionRecord
            {
                QueryName = $"{operationName}_Progress",
                Duration = TimeSpan.Zero,
                IsSuccessful = true,
                Timestamp = DateTime.UtcNow,
                Properties = new Dictionary<string, object>
                {
                    { "CurrentBatch", currentBatch },
                    { "TotalBatches", totalBatches },
                    { "ProgressPercentage", progressPercentage }
                }
            };

            _queryRecords.AddOrUpdate($"{operationName}_Progress",
                new List<QueryExecutionRecord> { progressRecord },
                (key, existing) =>
                {
                    existing.Add(progressRecord);
                    // Keep only last 100 progress records
                    if (existing.Count > 100)
                    {
                        existing.RemoveRange(0, existing.Count - 100);
                    }
                    return existing;
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting batch progress for {OperationName}", operationName);
        }

        return Task.CompletedTask;
    }

    public Task ReportBulkOperationAsync(string operationName, int processedCount, TimeSpan elapsedTime)
    {
        try
        {
            var throughput = processedCount / elapsedTime.TotalSeconds;

            _logger.LogInformation("Bulk operation completed: {OperationName} - {ProcessedCount} items in {ElapsedMs}ms (Throughput: {Throughput:F2} items/sec)",
                operationName, processedCount, elapsedTime.TotalMilliseconds, throughput);

            // Record as a query execution with bulk operation metrics
            var bulkRecord = new QueryExecutionRecord
            {
                QueryName = operationName,
                Duration = elapsedTime,
                IsSuccessful = true,
                Timestamp = DateTime.UtcNow,
                Properties = new Dictionary<string, object>
                {
                    { "ProcessedCount", processedCount },
                    { "Throughput", throughput },
                    { "OperationType", "BulkOperation" }
                }
            };

            _queryRecords.AddOrUpdate(operationName,
                new List<QueryExecutionRecord> { bulkRecord },
                (key, existing) =>
                {
                    existing.Add(bulkRecord);
                    // Keep only last 500 bulk operation records
                    if (existing.Count > 500)
                    {
                        existing.RemoveRange(0, existing.Count - 500);
                    }
                    return existing;
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting bulk operation for {OperationName}", operationName);
        }

        return Task.CompletedTask;
    }

    private static string GetKeyPattern(string key)
    {
        // Simple pattern extraction - replace IDs with wildcards
        return System.Text.RegularExpressions.Regex.Replace(key, @"\d+", "*");
    }

    private class QueryExecutionRecord
    {
        public string QueryName { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object>? Properties { get; set; }
    }

    private class CacheOperationRecord
    {
        public string CacheKey { get; set; } = string.Empty;
        public bool IsHit { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; }
    }

    private class ApiEndpointRecord
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public int StatusCode { get; set; }
        public long? ResponseSize { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
