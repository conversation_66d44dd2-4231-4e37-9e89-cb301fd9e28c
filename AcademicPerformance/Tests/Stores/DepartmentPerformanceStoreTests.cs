using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Stores;

/// <summary>
/// Unit tests for DepartmentPerformanceStore refactored methods
/// </summary>
public class DepartmentPerformanceStoreTests : IDisposable
{
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<ILogger<DepartmentPerformanceStore>> _mockLogger;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly DepartmentPerformanceStore _store;

    public DepartmentPerformanceStoreTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockLogger = new Mock<ILogger<DepartmentPerformanceStore>>();
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        _store = new DepartmentPerformanceStore(_context, _mockLogger.Object, _mockPerformanceService.Object);
    }

    [Fact]
    public async Task BulkInsertAsync_WithValidEntities_ShouldInsertSuccessfully()
    {
        // Arrange
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "dept1",
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                ResearchScore = 85,
                TeachingScore = 90,
                ServiceScore = 80,
                InnovationScore = 75
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "dept2",
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                ResearchScore = 88,
                TeachingScore = 92,
                ServiceScore = 85,
                InnovationScore = 78
            }
        };

        // Act
        var result = await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);
        Assert.True(result.ElapsedTime > TimeSpan.Zero);

        // Verify entities were inserted with business logic applied
        var insertedEntities = await _context.DepartmentPerformances.ToListAsync();
        Assert.Equal(2, insertedEntities.Count);
        
        foreach (var entity in insertedEntities)
        {
            Assert.True(entity.OverallScore > 0); // Performance calculation applied
            Assert.NotNull(entity.CreatedAt); // Audit fields applied
            Assert.NotNull(entity.UpdatedAt);
        }

        // Verify performance monitoring was called
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task BulkInsertAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "", // Invalid - empty
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                OverallScore = 150 // Invalid - out of range
            }
        };

        // Act
        var result = await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("DepartmentId boş olamaz", result.ErrorMessage);
        Assert.Contains("OverallScore 0-100 arasında olmalı", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkUpdateAsync_WithValidEntities_ShouldUpdateSuccessfully()
    {
        // Arrange - First insert entities
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "dept1",
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                ResearchScore = 85,
                TeachingScore = 90,
                ServiceScore = 80,
                InnovationScore = 75
            }
        };

        await _context.DepartmentPerformances.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Modify entities
        entities[0].ResearchScore = 95;
        entities[0].TeachingScore = 88;

        // Act
        var result = await _store.BulkUpdateAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(1, result.ProcessedCount);

        // Verify entity was updated with business logic applied
        var updatedEntity = await _context.DepartmentPerformances.FirstAsync();
        Assert.Equal(95, updatedEntity.ResearchScore);
        Assert.Equal(88, updatedEntity.TeachingScore);
        Assert.True(updatedEntity.OverallScore > 0); // Performance calculation applied
        Assert.NotNull(updatedEntity.UpdatedAt); // Audit fields applied
    }

    [Fact]
    public async Task BulkDeleteAsync_WithValidEntities_ShouldDeleteSuccessfully()
    {
        // Arrange - First insert entities
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "dept1",
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                ResearchScore = 85,
                TeachingScore = 90,
                ServiceScore = 80,
                InnovationScore = 75
            }
        };

        await _context.DepartmentPerformances.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _store.BulkDeleteAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(1, result.ProcessedCount);

        // Verify entity was deleted
        var remainingEntities = await _context.DepartmentPerformances.ToListAsync();
        Assert.Empty(remainingEntities);
    }

    [Fact]
    public async Task BulkDeleteAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = "", // Invalid - empty
                DepartmentId = "dept1",
                Period = "2024-1"
            }
        };

        // Act
        var result = await _store.BulkDeleteAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("Delete için entity Id boş olamaz", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkInsertAsync_WithCancellation_ShouldHandleCancellation()
    {
        // Arrange
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "dept1",
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                ResearchScore = 85,
                TeachingScore = 90,
                ServiceScore = 80,
                InnovationScore = 75
            }
        };

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act
        var result = await _store.BulkInsertAsync(entities, cancellationTokenSource.Token);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("canceled", result.ErrorMessage.ToLower());
    }

    [Fact]
    public async Task BulkOperations_ShouldLogAppropriateMessages()
    {
        // Arrange
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "dept1",
                Period = "2024-1",
                EvaluationDate = DateTime.UtcNow,
                ResearchScore = 85,
                TeachingScore = 90,
                ServiceScore = 80,
                InnovationScore = 75
            }
        };

        // Act
        await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert - Verify logging calls
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk insert başlatılıyor")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk insert tamamlandı")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
