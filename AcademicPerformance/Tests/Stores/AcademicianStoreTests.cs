using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Stores;

/// <summary>
/// Unit tests for AcademicianStore refactored methods
/// </summary>
public class AcademicianStoreTests : IDisposable
{
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<ILogger<AcademicianStore>> _mockLogger;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly AcademicianStore _store;

    public AcademicianStoreTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockLogger = new Mock<ILogger<AcademicianStore>>();
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        _store = new AcademicianStore(_context, _mockLogger.Object, _mockPerformanceService.Object);
    }

    [Fact]
    public async Task CreateAcademicianProfilesAsync_WithValidEntities_ShouldCreateSuccessfully()
    {
        // Arrange
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "user1",
                Name = "John",
                Surname = "Doe",
                Email = "<EMAIL>"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "user2",
                Name = "Jane",
                Surname = "Smith",
                Email = "<EMAIL>"
            }
        };

        // Act
        var result = await _store.CreateAcademicianProfilesAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);
        Assert.Equal(2, result.Data.Count);
        Assert.True(result.ElapsedTime > TimeSpan.Zero);

        // Verify entities were created with business logic applied
        var createdEntities = await _context.AcademicianProfiles.ToListAsync();
        Assert.Equal(2, createdEntities.Count);
        
        foreach (var entity in createdEntities)
        {
            Assert.True(entity.IsActive); // Business logic applied
            Assert.False(entity.Deleted);
            Assert.False(entity.Disabled);
            Assert.NotNull(entity.FullName);
            Assert.Contains(" ", entity.FullName);
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.NotNull(entity.LastSyncedAt);
        }

        // Verify performance monitoring was called
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task CreateAcademicianProfilesAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "", // Invalid - empty
                Name = "", // Invalid - empty
                Surname = "Doe"
            }
        };

        // Act
        var result = await _store.CreateAcademicianProfilesAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("UniversityUserId boş olamaz", result.ErrorMessage);
        Assert.Contains("Name boş olamaz", result.ErrorMessage);
        Assert.Empty(result.Data);
    }

    [Fact]
    public async Task UpdateAcademicianProfilesAsync_WithValidEntities_ShouldUpdateSuccessfully()
    {
        // Arrange - First create entities
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "user1",
                Name = "John",
                Surname = "Doe",
                Email = "<EMAIL>"
            }
        };

        await _context.AcademicianProfiles.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Modify entities
        entities[0].Name = "Johnny";
        entities[0].Email = "<EMAIL>";

        // Act
        var result = await _store.UpdateAcademicianProfilesAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(1, result.ProcessedCount);

        // Verify entity was updated with business logic applied
        var updatedEntity = await _context.AcademicianProfiles.FirstAsync();
        Assert.Equal("Johnny", updatedEntity.Name);
        Assert.Equal("<EMAIL>", updatedEntity.Email);
        Assert.Contains("Johnny", updatedEntity.FullName); // Business logic applied
        Assert.NotNull(updatedEntity.UpdatedAt);
    }

    [Fact]
    public async Task UpdateLastSyncedAtBulkAsync_WithValidUserIds_ShouldUpdateSuccessfully()
    {
        // Arrange - First create entities
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "user1",
                Name = "John",
                Surname = "Doe"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "user2",
                Name = "Jane",
                Surname = "Smith"
            }
        };

        await _context.AcademicianProfiles.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        var userIds = new List<string> { "user1", "user2" };
        var syncedAt = DateTime.UtcNow;
        var syncNotes = "Bulk sync completed";

        // Act
        var result = await _store.UpdateLastSyncedAtBulkAsync(userIds, syncedAt, syncNotes, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);

        // Verify entities were updated
        var updatedEntities = await _context.AcademicianProfiles.ToListAsync();
        foreach (var entity in updatedEntities)
        {
            Assert.Equal(syncedAt.Date, entity.LastSyncedAt?.Date);
            Assert.Equal("Synced", entity.SyncStatus);
            Assert.Equal(syncNotes, entity.SyncNotes);
            Assert.NotNull(entity.UpdatedAt);
        }
    }

    [Fact]
    public async Task UpdateLastSyncedAtBulkAsync_WithNonExistentUserIds_ShouldReturnZeroProcessed()
    {
        // Arrange
        var userIds = new List<string> { "nonexistent1", "nonexistent2" };
        var syncedAt = DateTime.UtcNow;

        // Act
        var result = await _store.UpdateLastSyncedAtBulkAsync(userIds, syncedAt, null, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(0, result.ProcessedCount);
    }

    [Fact]
    public async Task UpdateAcademicianProfilesAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                Id = "", // Invalid - empty
                UniversityUserId = "user1",
                Name = "John",
                Surname = "Doe"
            }
        };

        // Act
        var result = await _store.UpdateAcademicianProfilesAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("Update için entity Id boş olamaz", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkOperations_ShouldLogAppropriateMessages()
    {
        // Arrange
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UniversityUserId = "user1",
                Name = "John",
                Surname = "Doe"
            }
        };

        // Act
        await _store.CreateAcademicianProfilesAsync(entities, CancellationToken.None);

        // Assert - Verify logging calls
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk create başlatılıyor")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk create tamamlandı")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
