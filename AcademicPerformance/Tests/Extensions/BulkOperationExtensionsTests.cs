using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Tests.Extensions;

/// <summary>
/// Unit tests for BulkOperationExtensions
/// </summary>
public class BulkOperationExtensionsTests : IDisposable
{
    private readonly DbContext _context;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly Mock<ILogger<BulkOperationExtensions>> _mockLogger;
    private readonly DatabasePerformanceConfiguration _config;

    public BulkOperationExtensionsTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TestDbContext(options);
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();
        _mockLogger = new Mock<ILogger<BulkOperationExtensions>>();
        _config = new DatabasePerformanceConfiguration { BatchSize = 2 };

        // Setup service provider mock
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton(_mockLogger.Object);
        serviceCollection.AddSingleton(_config);
        serviceCollection.AddSingleton(_mockPerformanceService.Object);

        var serviceProvider = serviceCollection.BuildServiceProvider();

        // Set service provider on context using reflection
        var serviceProviderProperty = _context.GetType().BaseType?
            .GetProperty("ServiceProvider", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        serviceProviderProperty?.SetValue(_context, serviceProvider);
    }

    [Fact]
    public async Task BulkInsertAsync_WithValidEntities_ShouldInsertSuccessfully()
    {
        // Arrange
        var entities = new List<TestEntity>
        {
            new() { Id = 1, Name = "Test1" },
            new() { Id = 2, Name = "Test2" },
            new() { Id = 3, Name = "Test3" }
        };

        // Act
        var result = await _context.BulkInsertAsync(entities, cancellationToken: CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(3, result.ProcessedCount);
        Assert.True(result.ElapsedTime > TimeSpan.Zero);
        Assert.Equal(2, result.BatchCount); // 3 entities with batch size 2 = 2 batches

        // Verify entities were inserted
        var insertedEntities = await _context.Set<TestEntity>().ToListAsync();
        Assert.Equal(3, insertedEntities.Count);
    }

    [Fact]
    public async Task BulkInsertAsync_WithEmptyCollection_ShouldReturnSuccessWithZeroCount()
    {
        // Arrange
        var entities = new List<TestEntity>();

        // Act
        var result = await _context.BulkInsertAsync(entities, cancellationToken: CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Equal(0, result.BatchCount);
    }

    [Fact]
    public async Task BulkInsertAsync_WithPerformanceMonitoring_ShouldCallMonitoringService()
    {
        // Arrange
        var entities = new List<TestEntity>
        {
            new() { Id = 1, Name = "Test1" },
            new() { Id = 2, Name = "Test2" }
        };

        // Act
        var result = await _context.BulkInsertAsync(entities,
            performanceMonitor: _mockPerformanceService.Object,
            cancellationToken: CancellationToken.None);

        // Assert
        Assert.True(result.Success);

        // Verify monitoring calls
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);

        _mockPerformanceService.Verify(x => x.ReportBulkOperationAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<TimeSpan>()), Times.Once);
    }

    [Fact]
    public async Task BulkUpdateAsync_WithValidEntities_ShouldUpdateSuccessfully()
    {
        // Arrange
        var entities = new List<TestEntity>
        {
            new() { Id = 1, Name = "Original1" },
            new() { Id = 2, Name = "Original2" }
        };

        // Insert first
        await _context.Set<TestEntity>().AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Modify entities
        entities[0].Name = "Updated1";
        entities[1].Name = "Updated2";

        // Act
        var result = await _context.BulkUpdateAsync(entities, cancellationToken: CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);

        // Verify entities were updated
        var updatedEntities = await _context.Set<TestEntity>().ToListAsync();
        Assert.Contains(updatedEntities, e => e.Name == "Updated1");
        Assert.Contains(updatedEntities, e => e.Name == "Updated2");
    }

    [Fact]
    public async Task BulkDeleteAsync_WithValidEntities_ShouldDeleteSuccessfully()
    {
        // Arrange
        var entities = new List<TestEntity>
        {
            new() { Id = 1, Name = "ToDelete1" },
            new() { Id = 2, Name = "ToDelete2" }
        };

        // Insert first
        await _context.Set<TestEntity>().AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _context.BulkDeleteAsync(entities, cancellationToken: CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);

        // Verify entities were deleted
        var remainingEntities = await _context.Set<TestEntity>().ToListAsync();
        Assert.Empty(remainingEntities);
    }

    [Fact]
    public async Task BulkInsertAsync_WithCancellation_ShouldThrowOperationCanceledException()
    {
        // Arrange
        var entities = new List<TestEntity>
        {
            new() { Id = 1, Name = "Test1" }
        };

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _context.BulkInsertAsync(entities, cancellationToken: cancellationTokenSource.Token));
    }

    [Fact]
    public async Task BulkInsertAsync_WithCustomBatchSize_ShouldUseBatchSize()
    {
        // Arrange
        var entities = new List<TestEntity>
        {
            new() { Id = 1, Name = "Test1" },
            new() { Id = 2, Name = "Test2" },
            new() { Id = 3, Name = "Test3" }
        };

        // Act
        var result = await _context.BulkInsertAsync(entities, batchSize: 1, cancellationToken: CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(3, result.ProcessedCount);
        Assert.Equal(3, result.BatchCount); // 3 entities with batch size 1 = 3 batches
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}

/// <summary>
/// Test DbContext for unit tests
/// </summary>
public class TestDbContext : DbContext
{
    public TestDbContext(DbContextOptions<TestDbContext> options) : base(options)
    {
    }

    public DbSet<TestEntity> TestEntities { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<TestEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100);
        });
    }
}

/// <summary>
/// Test entity for unit tests
/// </summary>
public class TestEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
}

/// <summary>
/// Unit tests for EntityBusinessLogicExtensions
/// </summary>
public class EntityBusinessLogicExtensionsTests
{
    [Fact]
    public void ApplyAuditFields_WithEntities_ShouldSetAuditFields()
    {
        // Arrange
        var entities = new List<AuditableTestEntity>
        {
            new() { Id = 1, Name = "Test1" },
            new() { Id = 2, Name = "Test2" }
        };

        var timestamp = DateTime.UtcNow;
        var userId = "test-user-123";

        // Act
        entities.ApplyAuditFields(timestamp, userId);

        // Assert
        foreach (var entity in entities)
        {
            Assert.Equal(timestamp, entity.CreatedAt);
            Assert.Equal(timestamp, entity.UpdatedAt);
            Assert.Equal(userId, entity.CreatedByUserId);
            Assert.Equal(userId, entity.UpdatedByUserId);
        }
    }

    [Fact]
    public void ApplyAcademicianBusinessLogic_WithValidEntities_ShouldApplyBusinessLogic()
    {
        // Arrange
        var entities = new List<AcademicianProfileEntity>
        {
            new()
            {
                UniversityUserId = "user1",
                Name = "John",
                Surname = "Doe"
            },
            new()
            {
                UniversityUserId = "user2",
                Name = "Jane",
                Surname = "Smith"
            }
        };

        // Act
        entities.ApplyAcademicianBusinessLogic();

        // Assert
        foreach (var entity in entities)
        {
            Assert.True(entity.IsActive);
            Assert.False(entity.Deleted);
            Assert.False(entity.Disabled);
            Assert.NotNull(entity.FullName);
            Assert.Contains(" ", entity.FullName);
            Assert.Equal("Pending", entity.SyncStatus);
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.NotNull(entity.LastSyncedAt);
        }
    }

    [Fact]
    public void ApplyPerformanceCalculations_WithValidScores_ShouldCalculateCorrectly()
    {
        // Arrange
        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                ResearchScore = 85,
                TeachingScore = 90,
                ServiceScore = 80,
                InnovationScore = 75
            }
        };

        // Act
        entities.ApplyPerformanceCalculations();

        // Assert
        var entity = entities.First();
        Assert.True(entity.OverallScore > 0);
        Assert.NotNull(entity.Status);
        Assert.NotNull(entity.PerformanceGrade);
        Assert.NotNull(entity.CreatedAt);
        Assert.NotNull(entity.UpdatedAt);
        Assert.NotNull(entity.CalculatedAt);
    }
}

/// <summary>
/// Test entity with audit fields
/// </summary>
public class AuditableTestEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedByUserId { get; set; }
    public string? UpdatedByUserId { get; set; }
}
